# Hướng dẫn cài đặt GPT-OSS Chatbot

## <PERSON><PERSON><PERSON> c<PERSON><PERSON> hệ thống

### Phần cứng tối thiểu
- **CPU**: Intel i5 hoặc AMD Ryzen 5 (khuyến nghị i7/Ryzen 7+)
- **RAM**: 16GB (khuyến nghị 32GB+)
- **Storage**: 50GB free space (SSD khuyến nghị)
- **GPU**: NVIDIA GPU với 16GB+ VRAM (tùy chọn nhưng khuyến nghị)

### Phần mềm
- **Python**: 3.8 trở lên
- **Git**: Đ<PERSON> clone repository
- **CUDA**: 11.8+ (nếu sử dụng GPU)

## Cài đặt nhanh

### 1. Clone repository
```bash
git clone <repository-url>
cd GPT_OSS
```

### 2. Cài đặt Python dependencies
```bash
pip install -r requirements.txt
```

### 3. Setup model (chọ<PERSON> một trong các options)

#### Option A: Sử dụng vLLM (k<PERSON><PERSON><PERSON><PERSON> nghị cho GPU)
```bash
python scripts/setup_model.py --backend vllm
```

#### Option B: Sử dụng Ollama (dễ cài đặt)
```bash
# Cài đặt Ollama từ https://ollama.com/download
python scripts/setup_model.py --backend ollama
```

#### Option C: Sử dụng Transformers (CPU/GPU)
```bash
python scripts/setup_model.py --backend transformers
```

### 4. Khởi động server
```bash
python scripts/run_server.py
```

### 5. Mở browser
Truy cập: http://localhost:8000

## Cài đặt chi tiết

### Cài đặt với vLLM

vLLM là backend hiệu suất cao nhất, phù hợp cho GPU mạnh:

```bash
# Cài đặt vLLM với GPT-OSS support
pip install --pre vllm==0.10.1+gptoss \
    --extra-index-url https://wheels.vllm.ai/gpt-oss/ \
    --extra-index-url https://download.pytorch.org/whl/nightly/cu128 \
    --index-strategy unsafe-best-match

# Download model
python scripts/setup_model.py --backend vllm

# Khởi động vLLM server
python scripts/run_server.py --backend vllm
```

### Cài đặt với Ollama

Ollama dễ cài đặt và sử dụng:

```bash
# 1. Cài đặt Ollama
# Windows: Download từ https://ollama.com/download
# macOS: brew install ollama
# Linux: curl -fsSL https://ollama.com/install.sh | sh

# 2. Pull GPT-OSS model
ollama pull gpt-oss:20b

# 3. Khởi động server
python scripts/run_server.py --backend ollama
```

### Cài đặt với Transformers

Sử dụng Transformers library trực tiếp:

```bash
# Cài đặt dependencies
pip install torch transformers accelerate

# Setup model
python scripts/setup_model.py --backend transformers

# Khởi động server
python scripts/run_server.py --backend transformers
```

## Cấu hình

### Model Configuration (configs/model_config.yaml)

```yaml
model:
  name: "gpt-oss-20b"
  model_id: "openai/gpt-oss-20b"
  max_tokens: 4096
  temperature: 0.7
  
inference:
  backend: "vllm"  # vllm, ollama, transformers
  
  vllm:
    host: "localhost"
    port: 8001
    gpu_memory_utilization: 0.9
    
  ollama:
    host: "localhost" 
    port: 11434
    model_name: "gpt-oss:20b"
```

### App Configuration (configs/app_config.yaml)

```yaml
app:
  title: "GPT-OSS Chatbot API"
  debug: true
  
server:
  host: "0.0.0.0"
  port: 8000
  
cors:
  allow_origins: ["*"]
```

## Troubleshooting

### Lỗi thường gặp

#### 1. "CUDA out of memory"
- Giảm `gpu_memory_utilization` trong config
- Giảm `max_tokens` 
- Sử dụng model nhỏ hơn

#### 2. "Model not found"
- Kiểm tra model đã được download
- Kiểm tra đường dẫn model trong config

#### 3. "Connection refused"
- Kiểm tra model server đã khởi động
- Kiểm tra port không bị conflict

#### 4. "Slow response"
- Sử dụng GPU thay vì CPU
- Tăng `tensor_parallel_size` cho vLLM
- Giảm `max_tokens`

### Performance tuning

#### Cho GPU mạnh (RTX 4090, H100)
```yaml
vllm:
  gpu_memory_utilization: 0.95
  max_model_len: 4096
  tensor_parallel_size: 1
```

#### Cho GPU yếu hơn (RTX 3080, 4070)
```yaml
vllm:
  gpu_memory_utilization: 0.8
  max_model_len: 2048
  tensor_parallel_size: 1
```

#### Cho CPU only
Sử dụng Ollama hoặc Transformers với:
```yaml
model:
  max_tokens: 1000
  temperature: 0.7
```

## Docker Setup (Optional)

```bash
# Build image
docker build -t gpt-oss-chatbot .

# Run container
docker run -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/models:/app/models \
  gpt-oss-chatbot
```

## Development Setup

```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest black isort flake8

# Run tests
pytest

# Format code
black .
isort .

# Lint
flake8 .
```

## Monitoring

### Health checks
- API health: http://localhost:8000/health
- Model status: http://localhost:8000/api/chat/status
- API docs: http://localhost:8000/docs

### Logs
- Application logs: `logs/app.log`
- Model logs: Check terminal output

## Support

Nếu gặp vấn đề:
1. Kiểm tra logs
2. Xem troubleshooting section
3. Tạo issue với thông tin chi tiết về lỗi
