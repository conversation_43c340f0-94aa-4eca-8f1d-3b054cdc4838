"""
Main FastAPI application for GPT-OSS Chatbot
"""

import os
from pathlib import Path
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse

from backend.app.routers import chat
from backend.app.utils.config import settings, app_config

# Create FastAPI app
app = FastAPI(
    title=settings.app_title,
    description="API for GPT-OSS-20b powered chatbot",
    version=settings.app_version,
    debug=settings.debug
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat.router)

# Setup static files and templates
frontend_dir = Path("frontend")
static_dir = frontend_dir / "static"
templates_dir = frontend_dir / "templates"

# Mount static files if directory exists
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Setup templates if directory exists
templates = None
if templates_dir.exists():
    templates = Jinja2Templates(directory=str(templates_dir))

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Serve main chatbot interface"""

    # Check if frontend/index.html exists
    frontend_index = Path("frontend/index.html")
    if frontend_index.exists():
        with open(frontend_index, 'r', encoding='utf-8') as f:
            return HTMLResponse(content=f.read())

    # Try to serve from templates
    if templates and (templates_dir / "index.html").exists():
        return templates.TemplateResponse("index.html", {"request": request})

    # Fallback to simple HTML if no template
    html_content = """
    <!DOCTYPE html>
    <html lang="vi">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>GPT-OSS Chatbot</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 600px;
                width: 100%;
            }
            h1 {
                color: #333;
                margin-bottom: 20px;
                font-size: 2.5em;
            }
            .status {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
            }
            .btn {
                background: #667eea;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                text-decoration: none;
                display: inline-block;
                margin: 10px;
                cursor: pointer;
                transition: background 0.3s;
            }
            .btn:hover {
                background: #5a6fd8;
            }
            .api-link {
                color: #667eea;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 GPT-OSS Chatbot</h1>
            <div class="status">
                <h3>Server đang chạy thành công!</h3>
                <p>Backend API đã sẵn sàng để phục vụ.</p>
            </div>
            
            <div>
                <a href="/docs" class="btn">📚 API Documentation</a>
                <a href="/api/chat/status" class="btn">🔍 Model Status</a>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Endpoints chính:</h3>
                <p><a href="/api/chat/status" class="api-link">GET /api/chat/status</a> - Kiểm tra trạng thái model</p>
                <p><a href="/api/chat/sessions" class="api-link">GET /api/chat/sessions</a> - Danh sách sessions</p>
                <p><strong>POST /api/chat/send</strong> - Gửi tin nhắn</p>
                <p><strong>POST /api/chat/stream</strong> - Stream response</p>
            </div>
            
            <div style="margin-top: 30px; font-size: 0.9em; color: #666;">
                <p>Để sử dụng giao diện chat đầy đủ, hãy tạo file frontend/index.html</p>
                <p>hoặc sử dụng API endpoints để tích hợp vào ứng dụng của bạn.</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app": settings.app_title,
        "version": settings.app_version,
        "backend": settings.model_backend
    }

@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    print(f"🚀 {settings.app_title} v{settings.app_version}")
    print(f"🔧 Model backend: {settings.model_backend}")
    print(f"🌐 Server: http://{settings.host}:{settings.port}")
    print(f"📚 API docs: http://{settings.host}:{settings.port}/docs")
    
    # Create necessary directories
    os.makedirs("data/conversations", exist_ok=True)
    os.makedirs("logs", exist_ok=True)

@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler"""
    print("👋 Shutting down GPT-OSS Chatbot...")

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "backend.app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
