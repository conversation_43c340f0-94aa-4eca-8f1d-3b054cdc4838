"""
Chat API endpoints
"""

import uuid
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from backend.app.models.chat import (
    ChatRequest, ChatResponse, ChatMessage, MessageRole,
    ChatSession, ChatHistory, ModelStatus, ReasoningLevel
)
from backend.app.services.model_service import model_service
from backend.app.utils.session import session_manager

router = APIRouter(prefix="/api/chat", tags=["chat"])

@router.post("/send", response_model=ChatResponse)
async def send_message(request: ChatRequest, background_tasks: BackgroundTasks):
    """Gửi message và nhận response từ model"""
    
    # Create session if not provided
    session_id = request.session_id
    if not session_id:
        session_id = session_manager.create_session()
    
    # Validate session exists
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Create user message
    user_message = ChatMessage(
        id=str(uuid.uuid4()),
        role=MessageRole.USER,
        content=request.message,
        timestamp=datetime.now()
    )
    
    # Add user message to session
    session_manager.add_message(session_id, user_message)
    
    # Get conversation history
    history = session_manager.get_session_history(session_id, limit=20)
    
    try:
        # Generate response from model
        response_content = ""
        async for chunk in model_service.generate_response(
            messages=history,
            reasoning_level=request.reasoning_level,
            temperature=request.temperature or 0.7,
            max_tokens=request.max_tokens or 1000,
            stream=False  # For non-streaming response
        ):
            response_content += chunk
        
        # Create assistant message
        assistant_message = ChatMessage(
            id=str(uuid.uuid4()),
            role=MessageRole.ASSISTANT,
            content=response_content,
            timestamp=datetime.now(),
            metadata={
                "reasoning_level": request.reasoning_level.value,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens
            }
        )
        
        # Add assistant message to session
        session_manager.add_message(session_id, assistant_message)
        
        return ChatResponse(
            message=response_content,
            session_id=session_id,
            message_id=assistant_message.id,
            reasoning_level=request.reasoning_level,
            timestamp=assistant_message.timestamp,
            metadata=assistant_message.metadata
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

@router.post("/stream")
async def stream_message(request: ChatRequest):
    """Stream response từ model"""
    
    # Create session if not provided
    session_id = request.session_id
    if not session_id:
        session_id = session_manager.create_session()
    
    # Validate session exists
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Create user message
    user_message = ChatMessage(
        id=str(uuid.uuid4()),
        role=MessageRole.USER,
        content=request.message,
        timestamp=datetime.now()
    )
    
    # Add user message to session
    session_manager.add_message(session_id, user_message)
    
    # Get conversation history
    history = session_manager.get_session_history(session_id, limit=20)
    
    async def generate_stream():
        """Generate streaming response"""
        try:
            response_content = ""
            
            # Send session info first
            yield f"data: {{'type': 'session', 'session_id': '{session_id}'}}\n\n"
            
            # Stream model response
            async for chunk in model_service.generate_response(
                messages=history,
                reasoning_level=request.reasoning_level,
                temperature=request.temperature or 0.7,
                max_tokens=request.max_tokens or 1000,
                stream=True
            ):
                response_content += chunk
                yield f"data: {{'type': 'content', 'content': '{chunk.replace(chr(10), chr(92) + chr(110))}'}}\n\n"
            
            # Create and save assistant message
            assistant_message = ChatMessage(
                id=str(uuid.uuid4()),
                role=MessageRole.ASSISTANT,
                content=response_content,
                timestamp=datetime.now(),
                metadata={
                    "reasoning_level": request.reasoning_level.value,
                    "temperature": request.temperature,
                    "max_tokens": request.max_tokens
                }
            )
            
            session_manager.add_message(session_id, assistant_message)
            
            # Send completion signal
            yield f"data: {{'type': 'done', 'message_id': '{assistant_message.id}'}}\n\n"
            
        except Exception as e:
            yield f"data: {{'type': 'error', 'error': '{str(e)}'}}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@router.get("/sessions", response_model=List[ChatSession])
async def list_sessions(limit: Optional[int] = 20):
    """Lấy danh sách chat sessions"""
    return session_manager.list_sessions(limit=limit)

@router.post("/sessions", response_model=dict)
async def create_session(title: Optional[str] = None):
    """Tạo session mới"""
    session_id = session_manager.create_session(title=title)
    return {"session_id": session_id}

@router.get("/sessions/{session_id}", response_model=ChatSession)
async def get_session(session_id: str):
    """Lấy thông tin session"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session

@router.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Xóa session"""
    success = session_manager.delete_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail="Session not found")
    return {"message": "Session deleted successfully"}

@router.get("/sessions/{session_id}/history", response_model=List[ChatMessage])
async def get_session_history(session_id: str, limit: Optional[int] = None):
    """Lấy lịch sử messages của session"""
    history = session_manager.get_session_history(session_id, limit=limit)
    if history is None:
        raise HTTPException(status_code=404, detail="Session not found")
    return history

@router.get("/status", response_model=ModelStatus)
async def get_model_status():
    """Lấy status của model"""
    return await model_service.get_status()

@router.get("/stats")
async def get_chat_stats():
    """Lấy thống kê chat"""
    stats = session_manager.get_stats()
    model_status = await model_service.get_status()

    return {
        "sessions": stats,
        "model": model_status.dict()
    }

@router.get("/sessions/{session_id}/export")
async def export_conversation(session_id: str, format: str = "json"):
    """Export conversation to different formats"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    if format.lower() == "json":
        from fastapi.responses import JSONResponse
        return JSONResponse(
            content=session.dict(),
            headers={"Content-Disposition": f"attachment; filename=chat_{session_id}.json"}
        )

    elif format.lower() == "txt":
        from fastapi.responses import PlainTextResponse

        # Convert to plain text
        text_content = f"Chat Session: {session.title}\n"
        text_content += f"Created: {session.created_at}\n"
        text_content += f"Updated: {session.updated_at}\n"
        text_content += "=" * 50 + "\n\n"

        for msg in session.messages:
            role = "User" if msg.role.value == "user" else "Assistant"
            timestamp = msg.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            text_content += f"[{timestamp}] {role}:\n{msg.content}\n\n"

        return PlainTextResponse(
            content=text_content,
            headers={"Content-Disposition": f"attachment; filename=chat_{session_id}.txt"}
        )

    elif format.lower() == "md":
        from fastapi.responses import PlainTextResponse

        # Convert to Markdown
        md_content = f"# {session.title}\n\n"
        md_content += f"**Created:** {session.created_at}  \n"
        md_content += f"**Updated:** {session.updated_at}  \n\n"
        md_content += "---\n\n"

        for msg in session.messages:
            role = "🧑 **User**" if msg.role.value == "user" else "🤖 **Assistant**"
            timestamp = msg.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            md_content += f"## {role} _{timestamp}_\n\n{msg.content}\n\n"

        return PlainTextResponse(
            content=md_content,
            headers={"Content-Disposition": f"attachment; filename=chat_{session_id}.md"}
        )

    else:
        raise HTTPException(status_code=400, detail="Unsupported format. Use: json, txt, or md")

@router.post("/sessions/{session_id}/title")
async def update_session_title(session_id: str, title: str):
    """Update session title"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    session.title = title
    session.updated_at = datetime.now()
    session_manager._save_session(session)

    return {"message": "Title updated successfully", "title": title}

@router.delete("/sessions/cleanup")
async def cleanup_old_sessions(days: int = 30):
    """Cleanup sessions older than specified days"""
    deleted_count = session_manager.clear_old_sessions(days)
    return {"message": f"Deleted {deleted_count} old sessions"}
