#!/usr/bin/env python3
"""
Quick demo script để chạy GPT-OSS Chatbot với mock backend
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    print("🚀 GPT-OSS Chatbot Demo")
    print("=" * 50)
    
    # Set environment for mock backend
    os.environ['MODEL_BACKEND'] = 'mock'
    
    # Create necessary directories
    os.makedirs("data/conversations", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    print("📁 Created necessary directories")
    print("🔧 Using mock backend for demo")
    print("🌐 Starting FastAPI server...")
    
    try:
        # Start the server
        cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print("💻 Server command:", " ".join(cmd))
        print("⏳ Starting server (this may take a few seconds)...")
        
        # Start server process
        process = subprocess.Popen(cmd, cwd=Path.cwd())
        
        # Wait a bit for server to start
        time.sleep(3)
        
        print("✅ Server started successfully!")
        print("🌐 Opening browser...")
        
        # Open browser
        webbrowser.open("http://localhost:8000")
        
        print("\n" + "=" * 50)
        print("🎉 GPT-OSS Chatbot Demo is running!")
        print("📱 URL: http://localhost:8000")
        print("📚 API Docs: http://localhost:8000/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("\n💡 Features to test:")
        print("   - Chat với mock GPT-OSS model")
        print("   - 3 reasoning levels (Low/Medium/High)")
        print("   - Real-time streaming response")
        print("   - Lưu lịch sử chat")
        print("   - Export conversations")
        print("\n⚠️  Đây là DEMO với mock backend")
        print("   Để sử dụng model thật, cài đặt Ollama hoặc vLLM")
        print("\n🛑 Nhấn Ctrl+C để tắt server")
        print("=" * 50)
        
        # Wait for process
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping server...")
        if 'process' in locals():
            process.terminate()
            process.wait()
        print("👋 Server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
