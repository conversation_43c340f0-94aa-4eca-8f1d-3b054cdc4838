"""
Session management utilities
"""

import uuid
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
from backend.app.models.chat import ChatSession, ChatMessage

class SessionManager:
    """Quản lý chat sessions"""
    
    def __init__(self, data_dir: str = "data/conversations"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self._sessions: Dict[str, ChatSession] = {}
        self._load_sessions()
    
    def _get_session_file(self, session_id: str) -> Path:
        """Get file path for session"""
        return self.data_dir / f"{session_id}.json"
    
    def _load_sessions(self):
        """Load existing sessions from disk"""
        for session_file in self.data_dir.glob("*.json"):
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                    session = ChatSession(**session_data)
                    self._sessions[session.session_id] = session
            except Exception as e:
                print(f"Error loading session {session_file}: {e}")
    
    def _save_session(self, session: ChatSession):
        """Save session to disk"""
        session_file = self._get_session_file(session.session_id)
        try:
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session.dict(), f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"Error saving session {session.session_id}: {e}")
    
    def create_session(self, title: Optional[str] = None) -> str:
        """Tạo session mới"""
        session_id = str(uuid.uuid4())
        session = ChatSession(
            session_id=session_id,
            title=title or f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        )
        
        self._sessions[session_id] = session
        self._save_session(session)
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Lấy session theo ID"""
        return self._sessions.get(session_id)
    
    def add_message(self, session_id: str, message: ChatMessage) -> bool:
        """Thêm message vào session"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        # Generate message ID if not provided
        if not message.id:
            message.id = str(uuid.uuid4())
        
        session.messages.append(message)
        session.updated_at = datetime.now()
        
        # Update title if this is the first user message
        if (len(session.messages) == 1 and 
            message.role.value == "user" and 
            session.title.startswith("Chat ")):
            # Use first 50 chars of message as title
            session.title = message.content[:50] + ("..." if len(message.content) > 50 else "")
        
        self._save_session(session)
        return True
    
    def get_session_history(self, session_id: str, limit: Optional[int] = None) -> List[ChatMessage]:
        """Lấy lịch sử messages của session"""
        session = self.get_session(session_id)
        if not session:
            return []
        
        messages = session.messages
        if limit:
            messages = messages[-limit:]
        
        return messages
    
    def list_sessions(self, limit: Optional[int] = None) -> List[ChatSession]:
        """List all sessions, sorted by updated_at desc"""
        sessions = sorted(
            self._sessions.values(),
            key=lambda s: s.updated_at,
            reverse=True
        )
        
        if limit:
            sessions = sessions[:limit]
        
        return sessions
    
    def delete_session(self, session_id: str) -> bool:
        """Xóa session"""
        if session_id not in self._sessions:
            return False
        
        # Remove from memory
        del self._sessions[session_id]
        
        # Remove file
        session_file = self._get_session_file(session_id)
        try:
            if session_file.exists():
                session_file.unlink()
            return True
        except Exception as e:
            print(f"Error deleting session file {session_id}: {e}")
            return False
    
    def clear_old_sessions(self, days: int = 30):
        """Xóa sessions cũ hơn N ngày"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        sessions_to_delete = [
            session_id for session_id, session in self._sessions.items()
            if session.updated_at < cutoff_date
        ]
        
        for session_id in sessions_to_delete:
            self.delete_session(session_id)
        
        return len(sessions_to_delete)
    
    def get_stats(self) -> Dict[str, int]:
        """Lấy thống kê sessions"""
        total_sessions = len(self._sessions)
        total_messages = sum(len(session.messages) for session in self._sessions.values())
        
        return {
            "total_sessions": total_sessions,
            "total_messages": total_messages
        }

# Global session manager instance
session_manager = SessionManager()
