# FastAPI Application Configuration

app:
  title: "GPT-OSS Chatbot API"
  description: "API for GPT-OSS-20b powered chatbot"
  version: "1.0.0"
  debug: true
  
server:
  host: "0.0.0.0"
  port: 8000
  reload: true
  workers: 1
  
# CORS settings
cors:
  allow_origins: ["*"]
  allow_credentials: true
  allow_methods: ["*"]
  allow_headers: ["*"]

# Database settings (SQLite for simplicity)
database:
  url: "sqlite:///./data/chatbot.db"
  echo: false
  
# Session management
session:
  secret_key: "your-secret-key-change-this-in-production"
  algorithm: "HS256"
  expire_minutes: 1440  # 24 hours

# Logging configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
  file: "./logs/app.log"
  rotation: "1 day"
  retention: "7 days"

# Rate limiting
rate_limit:
  requests_per_minute: 60
  burst_size: 10

# File upload settings
upload:
  max_file_size: 10485760  # 10MB
  allowed_extensions: [".txt", ".md", ".pdf"]
  upload_dir: "./data/uploads"

# Security settings
security:
  enable_auth: false  # Set to true for production
  jwt_secret: "your-jwt-secret-change-this"
  password_hash_rounds: 12
