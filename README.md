# GPT-OSS Chatbot Project

Dự án chatbot sử dụng GPT_OSS_20b với FastAPI backend và giao diện web đơn giản.

## Tính năng

- 🤖 Tích hợp GPT_OSS_20b model (21B parameters)
- 🚀 FastAPI backend với API RESTful
- 💬 Giao diện chat đơn giản và dễ sử dụng
- 📝 L<PERSON><PERSON> lịch sử hội thoại
- ⚙️ Cấu hình reasoning level (low/medium/high)
- 🔄 Real-time streaming response
- 📱 Responsive design

## Cấu trúc dự án

```
GPT_OSS/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI app chính
│   │   ├── models/         # Data models
│   │   ├── routers/        # API endpoints
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities
│   ├── requirements.txt
│   └── config.py
├── frontend/               # Web interface
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   ├── templates/
│   └── index.html
├── scripts/                # Setup và utility scripts
│   ├── setup_model.py
│   ├── install_deps.sh
│   └── run_server.py
├── configs/                # Configuration files
│   ├── model_config.yaml
│   └── app_config.yaml
├── data/                   # Data storage
│   └── conversations/
├── requirements.txt        # Main dependencies
├── docker-compose.yml      # Docker setup
└── README.md
```

## Yêu cầu hệ thống

- Python 3.8+
- RAM: Tối thiểu 16GB (khuyến nghị 32GB+)
- GPU: NVIDIA GPU với 16GB+ VRAM (tùy chọn)
- Disk: 50GB+ free space

## Cài đặt nhanh

### Option 1: Automatic Setup (Khuyến nghị)
```bash
# 1. Clone repository
git clone <repository-url>
cd GPT_OSS

# 2. Chạy script cài đặt tự động (Linux/macOS)
chmod +x scripts/install_deps.sh
./scripts/install_deps.sh --backend vllm

# 3. Setup model
python scripts/setup_model.py --backend vllm

# 4. Khởi động server
python scripts/run_server.py --backend vllm

# 5. Mở browser
# http://localhost:8000
```

### Option 2: Manual Setup
```bash
# 1. Cài đặt dependencies
pip install -r requirements.txt

# 2. Setup model (chọn một backend)
python scripts/setup_model.py --backend vllm    # Cho GPU mạnh
python scripts/setup_model.py --backend ollama  # Dễ cài đặt
python scripts/setup_model.py --backend transformers  # Universal

# 3. Khởi động server
python scripts/run_server.py

# 4. Truy cập: http://localhost:8000
```

### Option 3: Docker
```bash
# Khởi động với Docker Compose
docker-compose up -d

# Truy cập: http://localhost:8000
```

## Backends được hỗ trợ

| Backend | Ưu điểm | Nhược điểm | Phù hợp cho |
|---------|---------|------------|-------------|
| **vLLM** | Hiệu suất cao, tối ưu GPU | Cần GPU mạnh | Production, GPU RTX 4090+ |
| **Ollama** | Dễ cài đặt, user-friendly | Hiệu suất trung bình | Development, GPU RTX 3080+ |
| **Transformers** | Universal, linh hoạt | Chậm nhất | CPU, GPU yếu |

## Demo Screenshots

### Giao diện chính
![Main Interface](docs/images/main-interface.png)

### Chat với GPT-OSS
![Chat Example](docs/images/chat-example.png)

### Settings và Export
![Settings](docs/images/settings-export.png)

## API Usage

### Python Example
```python
import requests

# Gửi tin nhắn
response = requests.post("http://localhost:8000/api/chat/send", json={
    "message": "Giải thích về quantum computing",
    "reasoning_level": "high",
    "temperature": 0.7
})

result = response.json()
print(result["message"])
```

### cURL Example
```bash
curl -X POST "http://localhost:8000/api/chat/send" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Viết một bài thơ về AI",
    "reasoning_level": "medium"
  }'
```

## Hướng dẫn chi tiết

- 📖 [Installation Guide](docs/INSTALLATION.md) - Hướng dẫn cài đặt chi tiết
- 🔌 [API Documentation](docs/API.md) - Tài liệu API đầy đủ
- 🐛 [Troubleshooting](docs/TROUBLESHOOTING.md) - Xử lý lỗi thường gặp
- 🚀 [Performance Tuning](docs/PERFORMANCE.md) - Tối ưu hiệu suất
