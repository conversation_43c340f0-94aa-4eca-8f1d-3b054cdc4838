"""
Service để tương tác với GPT-OSS model
Hỗ trợ vLLM, Ollama và Transformers backends
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import AsyncGenerator, List, Dict, Any, Optional
from backend.app.models.chat import ChatMessage, ReasoningLevel, ModelStatus
from backend.app.utils.config import model_config, settings

class ModelBackend(ABC):
    """Abstract base class cho model backends"""
    
    @abstractmethod
    async def generate_response(
        self, 
        messages: List[ChatMessage],
        reasoning_level: ReasoningLevel = ReasoningLevel.MEDIUM,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Generate response từ model"""
        pass
    
    @abstractmethod
    async def get_status(self) -> ModelStatus:
        """Get model status"""
        pass

class VLLMBackend(ModelBackend):
    """vLLM backend implementation"""
    
    def __init__(self):
        self.config = model_config.get('inference', {}).get('vllm', {})
        self.host = self.config.get('host', 'localhost')
        self.port = self.config.get('port', 8001)
        self.base_url = f"http://{self.host}:{self.port}"
        
    def _format_messages_for_harmony(
        self, 
        messages: List[ChatMessage], 
        reasoning_level: ReasoningLevel
    ) -> List[Dict[str, str]]:
        """Format messages cho harmony response format"""
        
        # Get reasoning prompt
        reasoning_prompts = model_config.get('model', {}).get('reasoning_levels', {})
        reasoning_prompt = reasoning_prompts.get(reasoning_level.value, "Reasoning: medium")
        
        # Get system prompt
        system_prompt = model_config.get('chat', {}).get('system_prompt', '')
        
        # Combine system prompt with reasoning level
        full_system_prompt = f"{system_prompt}\n\n{reasoning_prompt}"
        
        formatted_messages = [
            {"role": "system", "content": full_system_prompt}
        ]
        
        # Add conversation messages
        for msg in messages:
            formatted_messages.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        return formatted_messages
    
    async def generate_response(
        self,
        messages: List[ChatMessage],
        reasoning_level: ReasoningLevel = ReasoningLevel.MEDIUM,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Generate response using vLLM"""
        
        try:
            import httpx
            
            formatted_messages = self._format_messages_for_harmony(messages, reasoning_level)
            
            payload = {
                "model": model_config.get('model', {}).get('model_id', 'openai/gpt-oss-20b'),
                "messages": formatted_messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": stream
            }
            
            async with httpx.AsyncClient(timeout=300.0) as client:
                if stream:
                    async with client.stream(
                        "POST",
                        f"{self.base_url}/v1/chat/completions",
                        json=payload
                    ) as response:
                        response.raise_for_status()
                        
                        async for line in response.aiter_lines():
                            if line.startswith("data: "):
                                data = line[6:]  # Remove "data: " prefix
                                
                                if data.strip() == "[DONE]":
                                    break
                                
                                try:
                                    chunk = json.loads(data)
                                    if "choices" in chunk and len(chunk["choices"]) > 0:
                                        delta = chunk["choices"][0].get("delta", {})
                                        content = delta.get("content", "")
                                        if content:
                                            yield content
                                except json.JSONDecodeError:
                                    continue
                else:
                    response = await client.post(
                        f"{self.base_url}/v1/chat/completions",
                        json=payload
                    )
                    response.raise_for_status()
                    
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0]["message"]["content"]
                        yield content
                        
        except Exception as e:
            yield f"Error: {str(e)}"
    
    async def get_status(self) -> ModelStatus:
        """Get vLLM server status"""
        try:
            import httpx
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    return ModelStatus(
                        backend="vllm",
                        status="ready",
                        model_name=model_config.get('model', {}).get('model_id', 'openai/gpt-oss-20b')
                    )
                else:
                    return ModelStatus(
                        backend="vllm",
                        status="error",
                        model_name=model_config.get('model', {}).get('model_id', 'openai/gpt-oss-20b'),
                        error_message=f"HTTP {response.status_code}"
                    )
                    
        except Exception as e:
            return ModelStatus(
                backend="vllm",
                status="error", 
                model_name=model_config.get('model', {}).get('model_id', 'openai/gpt-oss-20b'),
                error_message=str(e)
            )

class OllamaBackend(ModelBackend):
    """Ollama backend implementation"""
    
    def __init__(self):
        self.config = model_config.get('inference', {}).get('ollama', {})
        self.host = self.config.get('host', 'localhost')
        self.port = self.config.get('port', 11434)
        self.model_name = self.config.get('model_name', 'gpt-oss:20b')
        
    async def generate_response(
        self,
        messages: List[ChatMessage],
        reasoning_level: ReasoningLevel = ReasoningLevel.MEDIUM,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Generate response using Ollama"""
        
        try:
            import httpx
            
            # Format messages for Ollama
            formatted_messages = []
            
            # Add system message with reasoning level
            reasoning_prompts = model_config.get('model', {}).get('reasoning_levels', {})
            reasoning_prompt = reasoning_prompts.get(reasoning_level.value, "Reasoning: medium")
            system_prompt = model_config.get('chat', {}).get('system_prompt', '')
            full_system_prompt = f"{system_prompt}\n\n{reasoning_prompt}"
            
            formatted_messages.append({
                "role": "system",
                "content": full_system_prompt
            })
            
            for msg in messages:
                formatted_messages.append({
                    "role": msg.role.value,
                    "content": msg.content
                })
            
            payload = {
                "model": self.model_name,
                "messages": formatted_messages,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            async with httpx.AsyncClient(timeout=300.0) as client:
                if stream:
                    async with client.stream(
                        "POST",
                        f"http://{self.host}:{self.port}/api/chat",
                        json=payload
                    ) as response:
                        response.raise_for_status()
                        
                        async for line in response.aiter_lines():
                            try:
                                chunk = json.loads(line)
                                if "message" in chunk:
                                    content = chunk["message"].get("content", "")
                                    if content:
                                        yield content
                                
                                if chunk.get("done", False):
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                else:
                    response = await client.post(
                        f"http://{self.host}:{self.port}/api/chat",
                        json=payload
                    )
                    response.raise_for_status()
                    
                    result = response.json()
                    if "message" in result:
                        content = result["message"]["content"]
                        yield content
                        
        except Exception as e:
            yield f"Error: {str(e)}"
    
    async def get_status(self) -> ModelStatus:
        """Get Ollama server status"""
        try:
            import httpx
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"http://{self.host}:{self.port}/api/tags")
                
                if response.status_code == 200:
                    return ModelStatus(
                        backend="ollama",
                        status="ready",
                        model_name=self.model_name
                    )
                else:
                    return ModelStatus(
                        backend="ollama",
                        status="error",
                        model_name=self.model_name,
                        error_message=f"HTTP {response.status_code}"
                    )
                    
        except Exception as e:
            return ModelStatus(
                backend="ollama",
                status="error",
                model_name=self.model_name,
                error_message=str(e)
            )

class ModelService:
    """Main model service"""
    
    def __init__(self):
        self.backend = self._create_backend()
    
    def _create_backend(self) -> ModelBackend:
        """Create appropriate backend based on configuration"""
        backend_type = settings.model_backend.lower()
        
        if backend_type == "vllm":
            return VLLMBackend()
        elif backend_type == "ollama":
            return OllamaBackend()
        else:
            raise ValueError(f"Unsupported backend: {backend_type}")
    
    async def generate_response(
        self,
        messages: List[ChatMessage],
        reasoning_level: ReasoningLevel = ReasoningLevel.MEDIUM,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Generate response from model"""
        async for chunk in self.backend.generate_response(
            messages, reasoning_level, temperature, max_tokens, stream
        ):
            yield chunk
    
    async def get_status(self) -> ModelStatus:
        """Get model status"""
        return await self.backend.get_status()

# Global model service instance
model_service = ModelService()
