# GPT-OSS Chatbot API Documentation

## Base URL
```
http://localhost:8000
```

## Authentication
Hiện tại API không yêu cầu authentication. Trong production, nên enable authentication trong config.

## Endpoints

### Chat Endpoints

#### POST /api/chat/send
G<PERSON>i tin nhắn và nhận response từ model.

**Request Body:**
```json
{
  "message": "Xin chào!",
  "session_id": "optional-session-id",
  "reasoning_level": "medium",
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

**Response:**
```json
{
  "message": "Xin chào! Tôi có thể giúp gì cho bạn?",
  "session_id": "uuid-session-id",
  "message_id": "uuid-message-id",
  "reasoning_level": "medium",
  "timestamp": "2025-01-01T12:00:00Z",
  "metadata": {
    "reasoning_level": "medium",
    "temperature": 0.7,
    "max_tokens": 1000
  }
}
```

#### POST /api/chat/stream
Stream response từ model (Server-Sent Events).

**Request Body:** Giống như `/api/chat/send`

**Response:** Stream of events
```
data: {"type": "session", "session_id": "uuid"}

data: {"type": "content", "content": "Xin "}

data: {"type": "content", "content": "chào!"}

data: {"type": "done", "message_id": "uuid"}
```

### Session Management

#### GET /api/chat/sessions
Lấy danh sách chat sessions.

**Query Parameters:**
- `limit` (optional): Số lượng sessions tối đa (default: 20)

**Response:**
```json
[
  {
    "session_id": "uuid",
    "messages": [...],
    "created_at": "2025-01-01T12:00:00Z",
    "updated_at": "2025-01-01T12:30:00Z",
    "title": "Chat về AI",
    "metadata": {}
  }
]
```

#### POST /api/chat/sessions
Tạo session mới.

**Request Body:**
```json
{
  "title": "Chat mới"  // optional
}
```

**Response:**
```json
{
  "session_id": "uuid"
}
```

#### GET /api/chat/sessions/{session_id}
Lấy thông tin session cụ thể.

**Response:**
```json
{
  "session_id": "uuid",
  "messages": [
    {
      "id": "uuid",
      "role": "user",
      "content": "Xin chào!",
      "timestamp": "2025-01-01T12:00:00Z",
      "metadata": null
    },
    {
      "id": "uuid", 
      "role": "assistant",
      "content": "Xin chào! Tôi có thể giúp gì cho bạn?",
      "timestamp": "2025-01-01T12:00:05Z",
      "metadata": {
        "reasoning_level": "medium"
      }
    }
  ],
  "created_at": "2025-01-01T12:00:00Z",
  "updated_at": "2025-01-01T12:00:05Z",
  "title": "Chat về AI",
  "metadata": {}
}
```

#### DELETE /api/chat/sessions/{session_id}
Xóa session.

**Response:**
```json
{
  "message": "Session deleted successfully"
}
```

#### GET /api/chat/sessions/{session_id}/history
Lấy lịch sử messages của session.

**Query Parameters:**
- `limit` (optional): Số lượng messages tối đa

**Response:**
```json
[
  {
    "id": "uuid",
    "role": "user", 
    "content": "Xin chào!",
    "timestamp": "2025-01-01T12:00:00Z",
    "metadata": null
  }
]
```

### Export Features

#### GET /api/chat/sessions/{session_id}/export
Export conversation sang các format khác nhau.

**Query Parameters:**
- `format`: json, txt, hoặc md

**Response:** File download

#### POST /api/chat/sessions/{session_id}/title
Cập nhật title của session.

**Request Body:**
```json
{
  "title": "Title mới"
}
```

**Response:**
```json
{
  "message": "Title updated successfully",
  "title": "Title mới"
}
```

### System Endpoints

#### GET /api/chat/status
Kiểm tra trạng thái model.

**Response:**
```json
{
  "backend": "vllm",
  "status": "ready",
  "model_name": "openai/gpt-oss-20b",
  "memory_usage": 15.2,
  "gpu_usage": 85.5,
  "uptime": 3600.0,
  "error_message": null
}
```

#### GET /api/chat/stats
Lấy thống kê tổng quan.

**Response:**
```json
{
  "sessions": {
    "total_sessions": 25,
    "total_messages": 150
  },
  "model": {
    "backend": "vllm",
    "status": "ready",
    "model_name": "openai/gpt-oss-20b"
  }
}
```

#### DELETE /api/chat/sessions/cleanup
Dọn dẹp sessions cũ.

**Query Parameters:**
- `days` (optional): Xóa sessions cũ hơn N ngày (default: 30)

**Response:**
```json
{
  "message": "Deleted 5 old sessions"
}
```

#### GET /health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "app": "GPT-OSS Chatbot API",
  "version": "1.0.0",
  "backend": "vllm"
}
```

## Error Responses

Tất cả errors trả về format:
```json
{
  "detail": "Error message"
}
```

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request
- `404`: Not Found  
- `500`: Internal Server Error

## Rate Limiting

Default rate limits:
- 60 requests per minute
- Burst size: 10 requests

## WebSocket Support

Hiện tại chưa hỗ trợ WebSocket. Sử dụng Server-Sent Events cho streaming.

## Examples

### Gửi tin nhắn đơn giản
```bash
curl -X POST "http://localhost:8000/api/chat/send" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Giải thích về quantum computing",
    "reasoning_level": "high"
  }'
```

### Stream response
```bash
curl -X POST "http://localhost:8000/api/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Viết một bài thơ về AI",
    "stream": true
  }'
```

### Export conversation
```bash
curl "http://localhost:8000/api/chat/sessions/{session_id}/export?format=md" \
  -o conversation.md
```

## SDK Examples

### Python
```python
import requests

# Gửi tin nhắn
response = requests.post("http://localhost:8000/api/chat/send", json={
    "message": "Xin chào!",
    "reasoning_level": "medium"
})

result = response.json()
print(result["message"])
```

### JavaScript
```javascript
// Gửi tin nhắn
const response = await fetch('/api/chat/send', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        message: 'Xin chào!',
        reasoning_level: 'medium'
    })
});

const result = await response.json();
console.log(result.message);
```
