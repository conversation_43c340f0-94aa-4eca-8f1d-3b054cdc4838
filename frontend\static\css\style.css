/* GPT-OSS Chatbot Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    background: white;
    border-radius: 20px;
    margin: 10px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: 300px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.sidebar-header h2 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.new-chat-btn {
    width: 100%;
    padding: 12px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.new-chat-btn:hover {
    background: #5a6fd8;
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.chat-item {
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.chat-item:hover {
    background: #e9ecef;
    border-left-color: #667eea;
}

.chat-item.active {
    background: #667eea;
    color: white;
    border-left-color: #5a6fd8;
}

.chat-item-title {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-item-time {
    font-size: 12px;
    opacity: 0.7;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
}

.model-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-indicator {
    font-size: 8px;
}

.status-indicator.ready {
    color: #28a745;
}

.status-indicator.loading {
    color: #ffc107;
}

.status-indicator.error {
    color: #dc3545;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    display: none;
}

.chat-header h1 {
    flex: 1;
    color: #333;
    font-size: 1.5em;
}

.chat-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reasoning-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
}

.settings-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.3s;
}

.settings-btn:hover {
    background: #f8f9fa;
}

/* Settings Panel */
.settings-panel {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.settings-panel.open {
    max-height: 200px;
    padding: 20px;
}

.settings-content h3 {
    margin-bottom: 15px;
    color: #333;
}

.setting-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.setting-group label {
    min-width: 100px;
    font-size: 14px;
}

.setting-group input[type="range"] {
    flex: 1;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #fafafa;
}

.welcome-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-align: center;
}

.welcome-content {
    max-width: 500px;
}

.welcome-icon {
    font-size: 4em;
    color: #667eea;
    margin-bottom: 20px;
}

.welcome-content h2 {
    color: #333;
    margin-bottom: 10px;
}

.welcome-content p {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.quick-btn {
    padding: 10px 15px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.message {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: #667eea;
    color: white;
}

.message.assistant .message-avatar {
    background: #28a745;
    color: white;
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.message.user .message-content {
    text-align: right;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    line-height: 1.5;
    word-wrap: break-word;
}

.message.user .message-bubble {
    background: #667eea;
    color: white;
    border-bottom-right-radius: 6px;
}

.message.assistant .message-bubble {
    background: white;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 6px;
}

.message-time {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.message.user .message-time {
    text-align: right;
}

/* Chat Input */
.chat-input-container {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: white;
}

.chat-input-wrapper {
    display: flex;
    gap: 10px;
    align-items: flex-end;
    background: #f8f9fa;
    border-radius: 20px;
    padding: 10px;
}

#messageInput {
    flex: 1;
    border: none;
    background: none;
    resize: none;
    outline: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    max-height: 120px;
    padding: 8px 12px;
}

#sendButton {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: #667eea;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    flex-shrink: 0;
}

#sendButton:hover:not(:disabled) {
    background: #5a6fd8;
    transform: scale(1.05);
}

#sendButton:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.typing-indicator {
    color: #667eea;
}

/* Streaming cursor animation */
.cursor {
    animation: blink 1s infinite;
    color: #667eea;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Export buttons */
.export-buttons {
    display: flex;
    gap: 5px;
    margin-top: 5px;
}

.export-btn {
    padding: 4px 8px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s;
}

.export-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Code formatting */
pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    overflow-x: auto;
    margin: 8px 0;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

pre code {
    background: none;
    padding: 0;
}

/* Enhanced message bubbles */
.message-bubble {
    position: relative;
}

.message-bubble::selection {
    background: rgba(102, 126, 234, 0.3);
}

/* Better scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content i {
    font-size: 3em;
    margin-bottom: 20px;
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        margin: 0;
        border-radius: 0;
    }
    
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100%;
        z-index: 100;
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .sidebar-toggle {
        display: block !important;
    }
    
    .chat-header h1 {
        font-size: 1.2em;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .message-content {
        max-width: 85%;
    }
}
