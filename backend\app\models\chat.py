"""
Data models cho chat functionality
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum

class ReasoningLevel(str, Enum):
    """Reasoning levels cho GPT-OSS"""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"

class MessageRole(str, Enum):
    """Roles cho chat messages"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class ChatMessage(BaseModel):
    """Single chat message"""
    id: Optional[str] = None
    role: MessageRole
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None

class ChatRequest(BaseModel):
    """Request để gửi chat message"""
    message: str = Field(..., min_length=1, max_length=4000)
    session_id: Optional[str] = None
    reasoning_level: ReasoningLevel = ReasoningLevel.MEDIUM
    temperature: Optional[float] = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(default=1000, ge=1, le=4000)
    stream: bool = True

class ChatResponse(BaseModel):
    """Response từ chat API"""
    message: str
    session_id: str
    message_id: str
    reasoning_level: ReasoningLevel
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None

class ChatSession(BaseModel):
    """Chat session với lịch sử messages"""
    session_id: str
    messages: List[ChatMessage] = []
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    title: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class ChatHistory(BaseModel):
    """Lịch sử chat sessions"""
    sessions: List[ChatSession] = []
    total_sessions: int = 0
    total_messages: int = 0

class StreamChunk(BaseModel):
    """Chunk data cho streaming response"""
    content: str
    is_final: bool = False
    metadata: Optional[Dict[str, Any]] = None

class ModelStatus(BaseModel):
    """Status của model backend"""
    backend: str
    status: str  # "ready", "loading", "error"
    model_name: str
    memory_usage: Optional[float] = None
    gpu_usage: Optional[float] = None
    uptime: Optional[float] = None
    error_message: Optional[str] = None

class ChatSettings(BaseModel):
    """Cài đặt cho chat"""
    reasoning_level: ReasoningLevel = ReasoningLevel.MEDIUM
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=1000, ge=1, le=4000)
    system_prompt: Optional[str] = None
    enable_streaming: bool = True
    save_history: bool = True
