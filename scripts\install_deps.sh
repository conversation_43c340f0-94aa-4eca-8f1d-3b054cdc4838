#!/bin/bash
# Script cài đặt dependencies cho GPT-OSS Chatbot

set -e

echo "🚀 GPT-OSS Chatbot - Dependency Installation Script"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is installed
check_python() {
    print_status "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_success "Python $PYTHON_VERSION found"
        
        # Check if version is 3.8+
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
            print_success "Python version is compatible"
        else
            print_error "Python 3.8+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.8+"
        exit 1
    fi
}

# Check if pip is installed
check_pip() {
    print_status "Checking pip installation..."
    
    if command -v pip3 &> /dev/null; then
        print_success "pip3 found"
    elif command -v pip &> /dev/null; then
        print_success "pip found"
    else
        print_error "pip not found. Installing pip..."
        python3 -m ensurepip --upgrade
    fi
}

# Install base dependencies
install_base_deps() {
    print_status "Installing base dependencies..."
    
    pip3 install --upgrade pip
    pip3 install -r requirements.txt
    
    print_success "Base dependencies installed"
}

# Install backend-specific dependencies
install_backend_deps() {
    local backend=$1
    
    print_status "Installing dependencies for backend: $backend"
    
    case $backend in
        "vllm")
            print_status "Installing vLLM with GPT-OSS support..."
            pip3 install --pre vllm==0.10.1+gptoss \
                --extra-index-url https://wheels.vllm.ai/gpt-oss/ \
                --extra-index-url https://download.pytorch.org/whl/nightly/cu128 \
                --index-strategy unsafe-best-match
            print_success "vLLM installed"
            ;;
        "ollama")
            print_status "Installing Ollama Python client..."
            pip3 install ollama>=0.1.7
            print_success "Ollama client installed"
            print_warning "Don't forget to install Ollama server from https://ollama.com/download"
            ;;
        "transformers")
            print_status "Installing Transformers dependencies..."
            pip3 install torch>=2.0.0 transformers>=4.35.0 accelerate>=0.24.0
            print_success "Transformers dependencies installed"
            ;;
        *)
            print_warning "Unknown backend: $backend. Installing all backends..."
            install_backend_deps "vllm"
            install_backend_deps "ollama" 
            install_backend_deps "transformers"
            ;;
    esac
}

# Install development dependencies
install_dev_deps() {
    print_status "Installing development dependencies..."
    
    pip3 install pytest>=7.4.0 pytest-asyncio>=0.21.0 \
                black>=23.0.0 isort>=5.12.0 flake8>=6.0.0
    
    print_success "Development dependencies installed"
}

# Check system requirements
check_system() {
    print_status "Checking system requirements..."
    
    # Check available memory
    if command -v free &> /dev/null; then
        MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
        print_status "Available memory: ${MEMORY_GB}GB"
        
        if [ "$MEMORY_GB" -lt 16 ]; then
            print_warning "Less than 16GB RAM detected. Model may run slowly."
        fi
    fi
    
    # Check for NVIDIA GPU
    if command -v nvidia-smi &> /dev/null; then
        GPU_INFO=$(nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits)
        print_success "NVIDIA GPU detected: $GPU_INFO"
    else
        print_warning "No NVIDIA GPU detected. Will use CPU inference."
    fi
    
    # Check disk space
    DISK_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    print_status "Available disk space: ${DISK_SPACE}GB"
    
    if [ "$DISK_SPACE" -lt 50 ]; then
        print_warning "Less than 50GB disk space available. May not be enough for models."
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p data/conversations
    mkdir -p logs
    mkdir -p models
    
    print_success "Directories created"
}

# Main installation function
main() {
    local backend="vllm"
    local install_dev=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend)
                backend="$2"
                shift 2
                ;;
            --dev)
                install_dev=true
                shift
                ;;
            --help)
                echo "Usage: $0 [--backend vllm|ollama|transformers] [--dev]"
                echo ""
                echo "Options:"
                echo "  --backend    Choose inference backend (default: vllm)"
                echo "  --dev        Install development dependencies"
                echo "  --help       Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    print_status "Starting installation with backend: $backend"
    
    # Run installation steps
    check_python
    check_pip
    check_system
    create_directories
    install_base_deps
    install_backend_deps "$backend"
    
    if [ "$install_dev" = true ]; then
        install_dev_deps
    fi
    
    print_success "Installation completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "1. Run: python scripts/setup_model.py --backend $backend"
    echo "2. Run: python scripts/run_server.py --backend $backend"
    echo "3. Open: http://localhost:8000"
}

# Run main function with all arguments
main "$@"
