# GPT-OSS Model Configuration

model:
  name: "gpt-oss-20b"
  model_id: "openai/gpt-oss-20b"
  model_path: "./models/gpt-oss-20b"
  
  # Model parameters
  max_tokens: 4096
  temperature: 0.7
  top_p: 0.9
  top_k: 50
  
  # Reasoning levels
  reasoning_levels:
    low: "Reasoning: low"
    medium: "Reasoning: medium" 
    high: "Reasoning: high"
  
  default_reasoning: "medium"

# Inference backend configuration
inference:
  backend: "vllm"  # Options: vllm, ollama, transformers
  
  # vLLM settings
  vllm:
    host: "localhost"
    port: 8001
    gpu_memory_utilization: 0.9
    max_model_len: 4096
    tensor_parallel_size: 1
    
  # Ollama settings  
  ollama:
    host: "localhost"
    port: 11434
    model_name: "gpt-oss:20b"
    
  # Transformers settings
  transformers:
    device_map: "auto"
    torch_dtype: "auto"
    load_in_8bit: false
    load_in_4bit: false

# Chat settings
chat:
  max_history_length: 50
  system_prompt: |
    You are a helpful AI assistant powered by GPT-OSS-20b. 
    You provide accurate, helpful, and detailed responses.
    Always be polite and professional in your interactions.
  
  # Response streaming
  streaming: true
  chunk_size: 1024

# Performance settings
performance:
  max_concurrent_requests: 10
  request_timeout: 300
  cache_responses: true
  cache_ttl: 3600
