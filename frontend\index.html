<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-OSS Chatbot</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> GPT-OSS</h2>
                <button class="new-chat-btn" onclick="createNewChat()">
                    <i class="fas fa-plus"></i> Chat mới
                </button>
            </div>
            
            <div class="chat-history" id="chatHistory">
                <!-- Chat sessions will be loaded here -->
            </div>
            
            <div class="sidebar-footer">
                <div class="model-status" id="modelStatus">
                    <i class="fas fa-circle status-indicator"></i>
                    <span>Đang kiểm tra...</span>
                </div>
            </div>
        </div>
        
        <!-- Main Chat Area -->
        <div class="main-content">
            <!-- Header -->
            <div class="chat-header">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 id="chatTitle">GPT-OSS Chatbot</h1>
                <div class="chat-controls">
                    <select id="reasoningLevel" class="reasoning-select">
                        <option value="low">Reasoning: Low</option>
                        <option value="medium" selected>Reasoning: Medium</option>
                        <option value="high">Reasoning: High</option>
                    </select>
                    <button class="settings-btn" onclick="toggleSettings()">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
            
            <!-- Settings Panel -->
            <div class="settings-panel" id="settingsPanel">
                <div class="settings-content">
                    <h3>Cài đặt Chat</h3>
                    <div class="setting-group">
                        <label>Temperature:</label>
                        <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7">
                        <span id="temperatureValue">0.7</span>
                    </div>
                    <div class="setting-group">
                        <label>Max Tokens:</label>
                        <input type="range" id="maxTokens" min="100" max="4000" step="100" value="1000">
                        <span id="maxTokensValue">1000</span>
                    </div>
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="streamingEnabled" checked>
                            Streaming Response
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Chat Messages -->
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <div class="welcome-content">
                        <i class="fas fa-robot welcome-icon"></i>
                        <h2>Chào mừng đến với GPT-OSS Chatbot!</h2>
                        <p>Tôi là trợ lý AI được hỗ trợ bởi GPT-OSS-20b. Hãy bắt đầu cuộc trò chuyện!</p>
                        <div class="quick-actions">
                            <button class="quick-btn" onclick="sendQuickMessage('Xin chào! Bạn có thể giúp gì cho tôi?')">
                                👋 Chào hỏi
                            </button>
                            <button class="quick-btn" onclick="sendQuickMessage('Giải thích về quantum computing')">
                                🔬 Khoa học
                            </button>
                            <button class="quick-btn" onclick="sendQuickMessage('Viết một đoạn code Python đơn giản')">
                                💻 Lập trình
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chat Input -->
            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="Nhập tin nhắn của bạn..."
                        rows="1"
                        onkeydown="handleKeyDown(event)"
                    ></textarea>
                    <button id="sendButton" onclick="sendMessage()" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-footer">
                    <span class="char-count" id="charCount">0/4000</span>
                    <span class="typing-indicator" id="typingIndicator" style="display: none;">
                        <i class="fas fa-circle-notch fa-spin"></i> Đang trả lời...
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <i class="fas fa-robot fa-spin"></i>
            <p>Đang khởi tạo GPT-OSS...</p>
        </div>
    </div>
    
    <script src="/static/js/app.js"></script>
</body>
</html>
