#!/usr/bin/env python3
"""
Script để khởi động GPT-OSS Chatbot server
Hỗ trợ khởi động cả model backend và FastAPI server
"""

import os
import sys
import time
import signal
import subprocess
import argparse
import yaml
import threading
from pathlib import Path

def load_config():
    """Load configuration files"""
    model_config_path = Path("configs/model_config.yaml")
    app_config_path = Path("configs/app_config.yaml")
    
    configs = {}
    
    if model_config_path.exists():
        with open(model_config_path, 'r', encoding='utf-8') as f:
            configs['model'] = yaml.safe_load(f)
    
    if app_config_path.exists():
        with open(app_config_path, 'r', encoding='utf-8') as f:
            configs['app'] = yaml.safe_load(f)
    
    return configs

def start_vllm_server(config):
    """Khởi động vLLM server"""
    model_config = config['model']
    vllm_config = model_config['inference']['vllm']
    
    model_id = model_config['model']['model_id']
    host = vllm_config['host']
    port = vllm_config['port']
    
    print(f"🚀 Khởi động vLLM server cho model: {model_id}")
    
    cmd = [
        "vllm", "serve", model_id,
        "--host", host,
        "--port", str(port),
        "--gpu-memory-utilization", str(vllm_config['gpu_memory_utilization']),
        "--max-model-len", str(vllm_config['max_model_len']),
        "--tensor-parallel-size", str(vllm_config['tensor_parallel_size'])
    ]
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print(f"✅ vLLM server đang khởi động tại {host}:{port}")
        return process
    except Exception as e:
        print(f"❌ Lỗi khởi động vLLM server: {e}")
        return None

def check_ollama_server():
    """Kiểm tra Ollama server có đang chạy không"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False

def start_ollama_server():
    """Khởi động Ollama server nếu chưa chạy"""
    if check_ollama_server():
        print("✅ Ollama server đã đang chạy")
        return None
    
    print("🚀 Khởi động Ollama server...")
    try:
        process = subprocess.Popen(["ollama", "serve"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # Wait a bit for server to start
        time.sleep(3)
        
        if check_ollama_server():
            print("✅ Ollama server đã khởi động thành công")
            return process
        else:
            print("❌ Ollama server không thể khởi động")
            return None
            
    except Exception as e:
        print(f"❌ Lỗi khởi động Ollama server: {e}")
        return None

def start_fastapi_server(config):
    """Khởi động FastAPI server"""
    app_config = config.get('app', {})
    server_config = app_config.get('server', {})
    
    host = server_config.get('host', '0.0.0.0')
    port = server_config.get('port', 8000)
    reload = server_config.get('reload', True)
    
    print(f"🌐 Khởi động FastAPI server tại {host}:{port}")
    
    cmd = [
        sys.executable, "-m", "uvicorn",
        "backend.app.main:app",
        "--host", host,
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
    
    try:
        process = subprocess.Popen(cmd)
        print(f"✅ FastAPI server đang chạy tại http://{host}:{port}")
        return process
    except Exception as e:
        print(f"❌ Lỗi khởi động FastAPI server: {e}")
        return None

def wait_for_model_server(backend, config):
    """Đợi model server sẵn sàng"""
    if backend == "vllm":
        vllm_config = config['model']['inference']['vllm']
        url = f"http://{vllm_config['host']}:{vllm_config['port']}/health"
        
        print("⏳ Đợi vLLM server sẵn sàng...")
        for i in range(60):  # Wait up to 5 minutes
            try:
                import requests
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print("✅ vLLM server đã sẵn sàng")
                    return True
            except:
                pass
            
            time.sleep(5)
            print(f"⏳ Đang đợi... ({i+1}/60)")
        
        print("❌ vLLM server không sẵn sàng sau 5 phút")
        return False
        
    elif backend == "ollama":
        return check_ollama_server()
    
    return True

def signal_handler(signum, frame, processes):
    """Handle shutdown signals"""
    print("\n🛑 Đang tắt servers...")
    
    for process in processes:
        if process and process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
    
    print("👋 Servers đã được tắt")
    sys.exit(0)

def main():
    parser = argparse.ArgumentParser(description="Run GPT-OSS Chatbot Server")
    parser.add_argument("--backend", choices=["vllm", "ollama", "transformers"],
                       default="vllm", help="Model backend")
    parser.add_argument("--api-only", action="store_true",
                       help="Chỉ chạy FastAPI server (model server đã chạy riêng)")
    parser.add_argument("--model-only", action="store_true", 
                       help="Chỉ chạy model server")
    
    args = parser.parse_args()
    
    print("🚀 GPT-OSS Chatbot Server")
    print("=" * 50)
    
    # Load configs
    config = load_config()
    
    processes = []
    
    try:
        # Start model server
        if not args.api_only:
            if args.backend == "vllm":
                vllm_process = start_vllm_server(config)
                if vllm_process:
                    processes.append(vllm_process)
                    
                    # Wait for vLLM to be ready
                    if not wait_for_model_server("vllm", config):
                        print("❌ vLLM server không sẵn sàng")
                        sys.exit(1)
                        
            elif args.backend == "ollama":
                ollama_process = start_ollama_server()
                if ollama_process:
                    processes.append(ollama_process)
        
        # Start FastAPI server
        if not args.model_only:
            # Set environment variable for backend
            os.environ['MODEL_BACKEND'] = args.backend
            
            fastapi_process = start_fastapi_server(config)
            if fastapi_process:
                processes.append(fastapi_process)
        
        if not processes:
            print("❌ Không có server nào được khởi động")
            sys.exit(1)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, lambda s, f: signal_handler(s, f, processes))
        signal.signal(signal.SIGTERM, lambda s, f: signal_handler(s, f, processes))
        
        print("\n🎉 Servers đã khởi động thành công!")
        print("📝 Nhấn Ctrl+C để tắt servers")
        
        if not args.model_only:
            app_config = config.get('app', {})
            server_config = app_config.get('server', {})
            host = server_config.get('host', '0.0.0.0')
            port = server_config.get('port', 8000)
            
            if host == "0.0.0.0":
                print(f"🌐 Chatbot interface: http://localhost:{port}")
            else:
                print(f"🌐 Chatbot interface: http://{host}:{port}")
        
        # Wait for processes
        for process in processes:
            process.wait()
            
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None, processes)
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        signal_handler(signal.SIGTERM, None, processes)

if __name__ == "__main__":
    main()
