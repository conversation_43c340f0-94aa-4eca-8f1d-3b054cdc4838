// GPT-OSS Chatbot Frontend JavaScript

class ChatApp {
    constructor() {
        this.currentSessionId = null;
        this.isTyping = false;
        this.sessions = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadInitialData();
    }
    
    initializeElements() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.chatHistory = document.getElementById('chatHistory');
        this.modelStatus = document.getElementById('modelStatus');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.settingsPanel = document.getElementById('settingsPanel');
        this.sidebar = document.getElementById('sidebar');
        
        // Settings elements
        this.temperatureSlider = document.getElementById('temperature');
        this.temperatureValue = document.getElementById('temperatureValue');
        this.maxTokensSlider = document.getElementById('maxTokens');
        this.maxTokensValue = document.getElementById('maxTokensValue');
        this.reasoningLevel = document.getElementById('reasoningLevel');
        this.streamingEnabled = document.getElementById('streamingEnabled');
        
        // Other elements
        this.charCount = document.getElementById('charCount');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.chatTitle = document.getElementById('chatTitle');
    }
    
    setupEventListeners() {
        // Message input
        this.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.updateSendButton();
            this.autoResize();
        });
        
        // Settings sliders
        this.temperatureSlider.addEventListener('input', (e) => {
            this.temperatureValue.textContent = e.target.value;
        });
        
        this.maxTokensSlider.addEventListener('input', (e) => {
            this.maxTokensValue.textContent = e.target.value;
        });
        
        // Auto-hide loading overlay after 3 seconds
        setTimeout(() => {
            this.hideLoading();
        }, 3000);
    }
    
    async loadInitialData() {
        await this.checkModelStatus();
        await this.loadChatSessions();
    }
    
    async checkModelStatus() {
        try {
            const response = await fetch('/api/chat/status');
            const status = await response.json();
            
            this.updateModelStatus(status);
        } catch (error) {
            console.error('Error checking model status:', error);
            this.updateModelStatus({
                status: 'error',
                error_message: 'Không thể kết nối với model'
            });
        }
    }
    
    updateModelStatus(status) {
        const indicator = this.modelStatus.querySelector('.status-indicator');
        const text = this.modelStatus.querySelector('span');
        
        indicator.className = 'fas fa-circle status-indicator';
        
        if (status.status === 'ready') {
            indicator.classList.add('ready');
            text.textContent = `${status.model_name} - Sẵn sàng`;
        } else if (status.status === 'loading') {
            indicator.classList.add('loading');
            text.textContent = 'Đang tải model...';
        } else {
            indicator.classList.add('error');
            text.textContent = status.error_message || 'Lỗi model';
        }
    }
    
    async loadChatSessions() {
        try {
            const response = await fetch('/api/chat/sessions?limit=20');
            this.sessions = await response.json();
            this.renderChatHistory();
        } catch (error) {
            console.error('Error loading chat sessions:', error);
        }
    }
    
    renderChatHistory() {
        this.chatHistory.innerHTML = '';
        
        this.sessions.forEach(session => {
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-item';
            if (session.session_id === this.currentSessionId) {
                chatItem.classList.add('active');
            }
            
            const title = session.title || 'Chat mới';
            const time = new Date(session.updated_at).toLocaleDateString('vi-VN');
            
            chatItem.innerHTML = `
                <div class="chat-item-title">${title}</div>
                <div class="chat-item-time">${time}</div>
                <div class="export-buttons" style="display: none;">
                    <button class="export-btn" onclick="exportChat('${session.session_id}', 'json')">JSON</button>
                    <button class="export-btn" onclick="exportChat('${session.session_id}', 'txt')">TXT</button>
                    <button class="export-btn" onclick="exportChat('${session.session_id}', 'md')">MD</button>
                </div>
            `;

            // Show export buttons on hover
            chatItem.addEventListener('mouseenter', () => {
                const exportButtons = chatItem.querySelector('.export-buttons');
                exportButtons.style.display = 'flex';
            });

            chatItem.addEventListener('mouseleave', () => {
                const exportButtons = chatItem.querySelector('.export-buttons');
                exportButtons.style.display = 'none';
            });
            
            chatItem.addEventListener('click', () => {
                this.loadChatSession(session.session_id);
            });
            
            this.chatHistory.appendChild(chatItem);
        });
    }
    
    async loadChatSession(sessionId) {
        try {
            const response = await fetch(`/api/chat/sessions/${sessionId}`);
            const session = await response.json();
            
            this.currentSessionId = sessionId;
            this.chatTitle.textContent = session.title || 'GPT-OSS Chatbot';
            
            this.renderMessages(session.messages);
            this.renderChatHistory(); // Update active state
            
            // Hide welcome message
            const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
            
        } catch (error) {
            console.error('Error loading chat session:', error);
        }
    }
    
    renderMessages(messages) {
        // Clear existing messages except welcome
        const existingMessages = this.chatMessages.querySelectorAll('.message');
        existingMessages.forEach(msg => msg.remove());
        
        messages.forEach(message => {
            this.addMessageToChat(message.role, message.content, message.timestamp);
        });
        
        this.scrollToBottom();
    }
    
    addMessageToChat(role, content, timestamp = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const avatar = role === 'user' ? 'fas fa-user' : 'fas fa-robot';
        const time = timestamp ? new Date(timestamp).toLocaleTimeString('vi-VN') : new Date().toLocaleTimeString('vi-VN');
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="${avatar}"></i>
            </div>
            <div class="message-content">
                <div class="message-bubble">${this.formatMessage(content)}</div>
                <div class="message-time">${time}</div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    formatMessage(content) {
        // Basic formatting for code blocks and line breaks
        return content
            .replace(/\n/g, '<br>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>');
    }
    
    updateCharCount() {
        const count = this.messageInput.value.length;
        this.charCount.textContent = `${count}/4000`;
        
        if (count > 3800) {
            this.charCount.style.color = '#dc3545';
        } else if (count > 3500) {
            this.charCount.style.color = '#ffc107';
        } else {
            this.charCount.style.color = '#666';
        }
    }
    
    updateSendButton() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasText || this.isTyping;
    }
    
    autoResize() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    showTyping() {
        this.isTyping = true;
        this.typingIndicator.style.display = 'block';
        this.updateSendButton();
    }
    
    hideTyping() {
        this.isTyping = false;
        this.typingIndicator.style.display = 'none';
        this.updateSendButton();
    }
    
    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    async sendMessage(message = null) {
        const messageText = message || this.messageInput.value.trim();

        if (!messageText || this.isTyping) {
            return;
        }

        // Hide welcome message
        const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }

        // Add user message to chat
        this.addMessageToChat('user', messageText);

        // Clear input
        this.messageInput.value = '';
        this.updateCharCount();
        this.updateSendButton();
        this.autoResize();

        // Show typing indicator
        this.showTyping();

        try {
            const requestData = {
                message: messageText,
                session_id: this.currentSessionId,
                reasoning_level: this.reasoningLevel.value,
                temperature: parseFloat(this.temperatureSlider.value),
                max_tokens: parseInt(this.maxTokensSlider.value),
                stream: this.streamingEnabled.checked
            };

            if (this.streamingEnabled.checked) {
                await this.handleStreamingResponse(requestData);
            } else {
                await this.handleRegularResponse(requestData);
            }

        } catch (error) {
            console.error('Error sending message:', error);
            this.addMessageToChat('assistant', 'Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại.');
        } finally {
            this.hideTyping();
        }
    }

    async handleRegularResponse(requestData) {
        const response = await fetch('/api/chat/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();

        // Update session ID if this is a new chat
        if (!this.currentSessionId) {
            this.currentSessionId = result.session_id;
            await this.loadChatSessions(); // Refresh session list
        }

        this.addMessageToChat('assistant', result.message);
    }

    async handleStreamingResponse(requestData) {
        const response = await fetch('/api/chat/stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        let assistantMessage = '';
        let messageElement = null;

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));

                            if (data.type === 'session' && !this.currentSessionId) {
                                this.currentSessionId = data.session_id;
                                await this.loadChatSessions();
                            } else if (data.type === 'content') {
                                assistantMessage += data.content;

                                // Create or update message element
                                if (!messageElement) {
                                    messageElement = this.createStreamingMessage();
                                }

                                this.updateStreamingMessage(messageElement, assistantMessage);
                            } else if (data.type === 'done') {
                                // Streaming complete
                                break;
                            } else if (data.type === 'error') {
                                throw new Error(data.error);
                            }
                        } catch (e) {
                            // Skip invalid JSON
                            continue;
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    }

    createStreamingMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant';

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="message-bubble"></div>
                <div class="message-time">${new Date().toLocaleTimeString('vi-VN')}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();

        return messageDiv;
    }

    updateStreamingMessage(messageElement, content) {
        const bubble = messageElement.querySelector('.message-bubble');
        bubble.innerHTML = this.formatMessage(content) + '<span class="cursor">|</span>';
        this.scrollToBottom();
    }

    async createNewChat() {
        try {
            const response = await fetch('/api/chat/sessions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();
            this.currentSessionId = result.session_id;

            // Clear chat messages
            const existingMessages = this.chatMessages.querySelectorAll('.message');
            existingMessages.forEach(msg => msg.remove());

            // Show welcome message
            const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'flex';
            }

            // Update title
            this.chatTitle.textContent = 'GPT-OSS Chatbot';

            // Refresh session list
            await this.loadChatSessions();

        } catch (error) {
            console.error('Error creating new chat:', error);
        }
    }
}

// Global functions for HTML event handlers
let chatApp;

function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function sendMessage() {
    if (chatApp) {
        chatApp.sendMessage();
    }
}

function sendQuickMessage(message) {
    if (chatApp) {
        chatApp.sendMessage(message);
    }
}

function createNewChat() {
    if (chatApp) {
        chatApp.createNewChat();
    }
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('open');
}

function toggleSettings() {
    const settingsPanel = document.getElementById('settingsPanel');
    settingsPanel.classList.toggle('open');
}

async function exportChat(sessionId, format) {
    try {
        const response = await fetch(`/api/chat/sessions/${sessionId}/export?format=${format}`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        // Get filename from Content-Disposition header
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = `chat_${sessionId}.${format}`;

        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Download file
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

    } catch (error) {
        console.error('Error exporting chat:', error);
        alert('Lỗi khi export chat. Vui lòng thử lại.');
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    chatApp = new ChatApp();
});
