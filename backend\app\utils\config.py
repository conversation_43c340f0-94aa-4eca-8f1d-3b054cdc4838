"""
Configuration utilities
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseSettings

class Settings(BaseSettings):
    """Application settings"""
    
    # App settings
    app_title: str = "GPT-OSS Chatbot API"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Model settings
    model_backend: str = "vllm"  # vllm, ollama, transformers
    model_name: str = "openai/gpt-oss-20b"
    
    # Database
    database_url: str = "sqlite:///./data/chatbot.db"
    
    # Security
    secret_key: str = "your-secret-key-change-this"
    
    # CORS
    cors_origins: list = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False

def load_yaml_config(config_path: str) -> Dict[str, Any]:
    """Load YAML configuration file"""
    path = Path(config_path)
    if not path.exists():
        return {}
    
    with open(path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_model_config() -> Dict[str, Any]:
    """Get model configuration"""
    return load_yaml_config("configs/model_config.yaml")

def get_app_config() -> Dict[str, Any]:
    """Get app configuration"""
    return load_yaml_config("configs/app_config.yaml")

def get_backend_from_env() -> str:
    """Get model backend from environment"""
    return os.getenv("MODEL_BACKEND", "vllm")

# Global settings instance
settings = Settings()

# Load configs
model_config = get_model_config()
app_config = get_app_config()

# Update settings from configs
if app_config:
    app_settings = app_config.get('app', {})
    server_settings = app_config.get('server', {})
    
    settings.app_title = app_settings.get('title', settings.app_title)
    settings.app_version = app_settings.get('version', settings.app_version)
    settings.debug = app_settings.get('debug', settings.debug)
    
    settings.host = server_settings.get('host', settings.host)
    settings.port = server_settings.get('port', settings.port)
    
    cors_settings = app_config.get('cors', {})
    settings.cors_origins = cors_settings.get('allow_origins', settings.cors_origins)
    
    db_settings = app_config.get('database', {})
    settings.database_url = db_settings.get('url', settings.database_url)

# Override backend from environment
settings.model_backend = get_backend_from_env()
