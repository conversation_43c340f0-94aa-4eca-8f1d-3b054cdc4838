"""
Mock model service for testing without actual model
"""

import asyncio
from typing import AsyncGenerator, List
from backend.app.models.chat import ChatMessage, ReasoningLevel, ModelStatus

class MockModelService:
    """Mock model service for testing"""
    
    def __init__(self):
        self.model_name = "mock-gpt-oss-20b"
        self.backend = "mock"
    
    async def generate_response(
        self,
        messages: List[ChatMessage],
        reasoning_level: ReasoningLevel = ReasoningLevel.MEDIUM,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Generate mock response"""
        
        # Get the last user message
        user_message = ""
        for msg in reversed(messages):
            if msg.role.value == "user":
                user_message = msg.content
                break
        
        # Generate mock response based on reasoning level
        if reasoning_level == ReasoningLevel.HIGH:
            response = f"""Tôi sẽ phân tích câu hỏi "{user_message}" một cách chi tiết:

🤔 **Phân tích:**
<PERSON><PERSON><PERSON> là một câu hỏi thú vị đòi hỏi suy ngh<PERSON> sâu sắc.

💡 **Reasoning (High Level):**
1. <PERSON><PERSON><PERSON> ti<PERSON>, tôi cần hiểu bối cảnh của câu hỏi
2. Sau đó, tôi sẽ xem xét các khía cạnh khác nhau
3. Cuối cùng, đưa ra câu trả lời toàn diện

📝 **Kết luận:**
Dựa trên phân tích trên, tôi có thể trả lời rằng đây là một demo của GPT-OSS Chatbot với reasoning level HIGH. 

Hệ thống đang hoạt động tốt và sẵn sàng xử lý các câu hỏi phức tạp!"""

        elif reasoning_level == ReasoningLevel.MEDIUM:
            response = f"""Cảm ơn bạn đã hỏi về "{user_message}".

🎯 **Trả lời (Medium Reasoning):**
Đây là một demo của GPT-OSS Chatbot. Tôi đang sử dụng mock service để mô phỏng GPT-OSS-20b model.

✨ **Tính năng hiện có:**
- Chat real-time với streaming response
- 3 reasoning levels (Low/Medium/High)
- Lưu lịch sử hội thoại
- Export conversations
- Giao diện đẹp và responsive

Hệ thống đang hoạt động ổn định!"""

        else:  # LOW
            response = f"""Xin chào! Bạn vừa hỏi: "{user_message}"

Đây là GPT-OSS Chatbot demo với reasoning level LOW. Hệ thống hoạt động tốt! 🚀"""

        # Simulate streaming
        if stream:
            words = response.split()
            for i, word in enumerate(words):
                if i == 0:
                    yield word
                else:
                    yield " " + word
                await asyncio.sleep(0.05)  # Simulate typing delay
        else:
            yield response
    
    async def get_status(self) -> ModelStatus:
        """Get mock model status"""
        return ModelStatus(
            backend="mock",
            status="ready",
            model_name=self.model_name,
            memory_usage=8.5,
            gpu_usage=0.0,
            uptime=3600.0
        )
