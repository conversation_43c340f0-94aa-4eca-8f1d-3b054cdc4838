# Main dependencies for GPT-OSS Chatbot Project

# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# GPT-OSS and AI dependencies
torch>=2.0.0
transformers>=4.35.0
accelerate>=0.24.0
bitsandbytes>=0.41.0

# vLLM for efficient inference (optional but recommended)
# vllm==0.10.1+gptoss --extra-index-url https://wheels.vllm.ai/gpt-oss/

# Alternative: Ollama Python client
ollama>=0.1.7

# Data handling
pydantic>=2.0.0
pydantic-settings>=2.0.0
sqlalchemy>=2.0.0
alembic>=1.12.0

# Utilities
python-dotenv>=1.0.0
pyyaml>=6.0
httpx>=0.25.0
aiofiles>=23.2.0

# Logging and monitoring
loguru>=0.7.0
prometheus-client>=0.19.0

# Development tools
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# CORS and middleware
python-cors>=1.7.0
