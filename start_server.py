#!/usr/bin/env python3
"""
Simple server starter for GPT-OSS Chatbot
"""

import os
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set environment variables
os.environ['MODEL_BACKEND'] = 'mock'
os.environ['PYTHONPATH'] = str(current_dir)

# Create necessary directories
os.makedirs("data/conversations", exist_ok=True)
os.makedirs("logs", exist_ok=True)

print("🚀 Starting GPT-OSS Chatbot Demo Server")
print("=" * 50)
print("🔧 Backend: Mock (for demo)")
print("🌐 URL: http://localhost:8000")
print("📚 API Docs: http://localhost:8000/docs")
print("=" * 50)

# Import and run
try:
    import uvicorn
    from backend.app.main import app
    
    # Run the server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure all dependencies are installed:")
    print("   pip install fastapi uvicorn")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error starting server: {e}")
    sys.exit(1)
