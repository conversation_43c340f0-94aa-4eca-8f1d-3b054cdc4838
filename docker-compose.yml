version: '3.8'

services:
  # GPT-OSS Chatbot API
  chatbot-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - MODEL_BACKEND=vllm
      - PYTHONPATH=/app
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./configs:/app/configs
      - ./models:/app/models
    depends_on:
      - model-server
    restart: unless-stopped
    networks:
      - chatbot-network

  # vLLM Model Server
  model-server:
    image: vllm/vllm-openai:latest
    ports:
      - "8001:8001"
    environment:
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./models:/models
    command: >
      --model openai/gpt-oss-20b
      --host 0.0.0.0
      --port 8001
      --gpu-memory-utilization 0.9
      --max-model-len 4096
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    networks:
      - chatbot-network

  # Ollama Alternative (comment out model-server above if using this)
  # ollama-server:
  #   image: ollama/ollama:latest
  #   ports:
  #     - "11434:11434"
  #   volumes:
  #     - ollama-data:/root/.ollama
  #   environment:
  #     - OLLAMA_HOST=0.0.0.0
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: 1
  #             capabilities: [gpu]
  #   restart: unless-stopped
  #   networks:
  #     - chatbot-network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - chatbot-network

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - chatbot-api
    restart: unless-stopped
    networks:
      - chatbot-network

volumes:
  redis-data:
  ollama-data:

networks:
  chatbot-network:
    driver: bridge
