#!/usr/bin/env python3
"""
Script để setup GPT-OSS-20b model
Hỗ trợ cài đặt qua vLLM, Ollama hoặc Transformers
"""

import os
import sys
import subprocess
import argparse
import yaml
from pathlib import Path

def load_config():
    """Load model configuration"""
    config_path = Path("configs/model_config.yaml")
    if not config_path.exists():
        print("❌ Không tìm thấy file config: configs/model_config.yaml")
        sys.exit(1)
    
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def check_system_requirements():
    """Kiểm tra yêu cầu hệ thống"""
    print("🔍 Kiểm tra yêu cầu hệ thống...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Cần Python 3.8 trở lên")
        return False
    
    # Check available memory (basic check)
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"💾 RAM khả dụng: {memory_gb:.1f}GB")
        
        if memory_gb < 16:
            print("⚠️  Cảnh báo: RAM < 16GB, có thể gặp vấn đề khi chạy model")
    except ImportError:
        print("⚠️  Không thể kiểm tra RAM (cần cài psutil)")
    
    # Check GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"🎮 GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            print("⚠️  Không phát hiện GPU CUDA")
    except ImportError:
        print("⚠️  PyTorch chưa được cài đặt")
    
    return True

def install_dependencies(backend="vllm"):
    """Cài đặt dependencies theo backend"""
    print(f"📦 Cài đặt dependencies cho backend: {backend}")
    
    base_deps = [
        "torch>=2.0.0",
        "transformers>=4.35.0", 
        "accelerate>=0.24.0",
        "fastapi>=0.104.1",
        "uvicorn[standard]>=0.24.0"
    ]
    
    if backend == "vllm":
        # Install vLLM with GPT-OSS support
        vllm_cmd = [
            sys.executable, "-m", "pip", "install", "--pre", 
            "vllm==0.10.1+gptoss",
            "--extra-index-url", "https://wheels.vllm.ai/gpt-oss/",
            "--extra-index-url", "https://download.pytorch.org/whl/nightly/cu128",
            "--index-strategy", "unsafe-best-match"
        ]
        
        print("🔧 Cài đặt vLLM với GPT-OSS support...")
        try:
            subprocess.run(vllm_cmd, check=True)
            print("✅ vLLM đã được cài đặt thành công")
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi cài đặt vLLM: {e}")
            return False
            
    elif backend == "ollama":
        # Install Ollama Python client
        ollama_deps = ["ollama>=0.1.7"]
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + ollama_deps, check=True)
            print("✅ Ollama client đã được cài đặt")
            print("📝 Lưu ý: Bạn cần cài đặt Ollama server riêng từ https://ollama.com/download")
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi cài đặt Ollama client: {e}")
            return False
    
    # Install base dependencies
    try:
        subprocess.run([sys.executable, "-m", "pip", "install"] + base_deps, check=True)
        print("✅ Base dependencies đã được cài đặt")
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi cài đặt base dependencies: {e}")
        return False
    
    return True

def download_model_vllm(config):
    """Download model cho vLLM"""
    model_id = config['model']['model_id']
    print(f"📥 Downloading model {model_id} cho vLLM...")
    
    try:
        from huggingface_hub import snapshot_download
        
        model_path = Path("models/gpt-oss-20b")
        model_path.mkdir(parents=True, exist_ok=True)
        
        snapshot_download(
            repo_id=model_id,
            local_dir=str(model_path),
            local_dir_use_symlinks=False
        )
        
        print(f"✅ Model đã được download vào: {model_path}")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi download model: {e}")
        return False

def setup_ollama_model(config):
    """Setup model cho Ollama"""
    model_name = config['inference']['ollama']['model_name']
    print(f"🦙 Setup Ollama model: {model_name}")
    
    try:
        # Pull model từ Ollama registry
        subprocess.run(["ollama", "pull", model_name], check=True)
        print(f"✅ Ollama model {model_name} đã được pull thành công")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi pull Ollama model: {e}")
        print("💡 Đảm bảo Ollama server đã được cài đặt và đang chạy")
        return False
    except FileNotFoundError:
        print("❌ Ollama command không tìm thấy")
        print("📝 Hãy cài đặt Ollama từ: https://ollama.com/download")
        return False

def main():
    parser = argparse.ArgumentParser(description="Setup GPT-OSS-20b model")
    parser.add_argument("--backend", choices=["vllm", "ollama", "transformers"], 
                       default="vllm", help="Inference backend")
    parser.add_argument("--skip-deps", action="store_true", 
                       help="Skip dependency installation")
    parser.add_argument("--skip-model", action="store_true",
                       help="Skip model download")
    
    args = parser.parse_args()
    
    print("🚀 GPT-OSS-20b Setup Script")
    print("=" * 50)
    
    # Load config
    config = load_config()
    
    # Check system requirements
    if not check_system_requirements():
        print("❌ System requirements không đáp ứng")
        sys.exit(1)
    
    # Install dependencies
    if not args.skip_deps:
        if not install_dependencies(args.backend):
            print("❌ Cài đặt dependencies thất bại")
            sys.exit(1)
    
    # Setup model
    if not args.skip_model:
        if args.backend == "vllm":
            if not download_model_vllm(config):
                sys.exit(1)
        elif args.backend == "ollama":
            if not setup_ollama_model(config):
                sys.exit(1)
        elif args.backend == "transformers":
            print("📝 Transformers backend sẽ tự động download model khi chạy lần đầu")
    
    print("\n🎉 Setup hoàn tất!")
    print(f"🔧 Backend: {args.backend}")
    print("📝 Tiếp theo:")
    print("   1. Chạy: python scripts/run_server.py")
    print("   2. Mở browser: http://localhost:8000")

if __name__ == "__main__":
    main()
